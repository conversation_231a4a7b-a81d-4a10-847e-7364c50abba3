# Cloudflare Rate Limiting Configuration

## Overview
This document provides the exact configuration details for Cloudflare Rate Limiting Rules to protect the application from DoS attacks, brute-force attempts, and automated abuse.

## Rate Limiting Rules Configuration

### Rule 1: Contact Form Protection
**Rule Name:** `Protect Contact Form API`

**Match Criteria:**
- URI Path: `/api/contact`
- Method: `POST`
- Zone: All zones

**Rate Limiting:**
- Characteristic: `IP Address`
- Rate: `5 requests`
- Period: `10 minutes` (600 seconds)

**Action:**
- Action: `Block`
- Duration: `1 hour` (3600 seconds)
- Response Code: `429`
- Response Body: `{"error": "Rate limit exceeded", "message": "Too many contact form submissions. Please try again in 1 hour."}`

**Cloudflare Dashboard Configuration:**
```
Rules > Rate Limiting Rules > Create Rule

Name: Protect Contact Form API
Expression: (http.request.uri.path eq "/api/contact" and http.request.method eq "POST")
Characteristics: IP Address
Rate: 5 requests per 10 minutes
Action: Block for 1 hour
```

### Rule 2: Search API Protection
**Rule Name:** `Protect Search API`

**Match Criteria:**
- URI Path: `/api/search*` (includes all search endpoints)
- Method: `GET`
- Zone: All zones

**Rate Limiting:**
- Characteristic: `IP Address`
- Rate: `100 requests`
- Period: `1 minute` (60 seconds)

**Action:**
- Action: `Block`
- Duration: `5 minutes` (300 seconds)
- Response Code: `429`
- Response Body: `{"error": "Rate limit exceeded", "message": "Too many search requests. Please try again in 5 minutes."}`

**Cloudflare Dashboard Configuration:**
```
Rules > Rate Limiting Rules > Create Rule

Name: Protect Search API
Expression: (http.request.uri.path matches "/api/search.*" and http.request.method eq "GET")
Characteristics: IP Address
Rate: 100 requests per 1 minute
Action: Block for 5 minutes
```

### Rule 3: General API Protection (Fallback)
**Rule Name:** `General API Rate Limit`

**Match Criteria:**
- URI Path: `/api/*` (all API endpoints)
- Method: `ANY`
- Zone: All zones

**Rate Limiting:**
- Characteristic: `IP Address`
- Rate: `200 requests`
- Period: `1 minute` (60 seconds)

**Action:**
- Action: `Block`
- Duration: `10 minutes` (600 seconds)
- Response Code: `429`
- Response Body: `{"error": "Rate limit exceeded", "message": "Too many API requests. Please try again in 10 minutes."}`

**Cloudflare Dashboard Configuration:**
```
Rules > Rate Limiting Rules > Create Rule

Name: General API Rate Limit
Expression: (http.request.uri.path matches "/api/.*")
Characteristics: IP Address
Rate: 200 requests per 1 minute
Action: Block for 10 minutes
```

## Rule Priority Order
1. **Contact Form Protection** (Priority: 1) - Most restrictive
2. **Search API Protection** (Priority: 2) - Specific to search
3. **General API Protection** (Priority: 3) - Fallback for all other APIs

## Environment Configuration
- **Development:** Rules disabled or set to very high limits for testing
- **Staging:** Same rules as production for realistic testing
- **Production:** Full protection with specified limits

## Monitoring and Alerts
- Set up Cloudflare alerts for rate limit triggers
- Monitor false positives and adjust thresholds if needed
- Track blocked requests in Cloudflare Analytics

## Implementation Steps
1. Log into Cloudflare Dashboard
2. Navigate to Security > WAF > Rate Limiting Rules
3. Create each rule in the specified priority order
4. Test with staging environment first
5. Deploy to production
6. Monitor for 24-48 hours and adjust if needed

## Testing
- Use tools like `curl` or `ab` (Apache Bench) to test rate limits
- Verify legitimate traffic is not blocked
- Confirm malicious traffic is properly blocked
- Test from different IP addresses to ensure rules work correctly

## Rollback Plan
If issues occur:
1. Disable specific rules causing problems
2. Adjust rate limits temporarily
3. Monitor application performance
4. Re-enable with modified settings

## Notes
- These rules work in conjunction with server-side rate limiting
- Cloudflare provides the first line of defense
- Server-side rate limiting acts as backup protection
- Rules can be fine-tuned based on traffic patterns and abuse attempts
