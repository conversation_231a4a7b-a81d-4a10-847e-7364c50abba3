{"$schema": "https://json.schemastore.org/package.json", "name": "cashback-deals", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "clean": "rm -rf .next", "clean:build": "npm run clean && npm run build", "start": "next start", "lint": "next lint", "seo-audit": "lighthouse --only=seo --output=html --output-path=./reports/seo-audit.html", "perf-test": "lighthouse --only=performance --output=html --output-path=./reports/performance.html", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:camelcase": "jest --testPathPatterns=camelcase", "test:transformations": "jest --testPathPatterns=transformations", "test:api": "jest --testPathPatterns=api", "test:metadata": "jest --testPathPatterns=metadata", "validate:schema": "node scripts/validate-structured-data.js", "audit:performance": "lighthouse --only=performance --output=json --quiet", "audit:seo": "lighthouse --only=seo --output=json --quiet", "generate:sitemap": "node scripts/generate-sitemap.js", "optimize:images": "next-optimized-images", "build:analyze": "ANALYZE=true npm run build", "preview": "npm run build && npm run start", "seo:test": "node scripts/seo-test.js", "seo:monitor": "curl -s http://localhost:3000/api/seo/monitor", "seo:validate-sitemap": "node -e \"console.log('Sitemap validation would run here')\"", "performance:check": "curl -s http://localhost:3000/api/analytics/web-vitals"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@shadcn/ui": "^0.0.4", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.47.13", "@tanstack/react-query-devtools": "^5.64.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "dotenv": "^16.4.7", "framer-motion": "^11.18.0", "graphql": "^16.10.0", "isomorphic-dompurify": "^2.26.0", "lucide-react": "^0.471.2", "next": "15.1.4", "next-auth": "^4.24.11", "next-i18next": "^15.4.1", "next-seo": "^6.6.0", "nodemailer": "^6.10.0", "papaparse": "^5.5.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-loading-skeleton": "^3.5.0", "shadcn-ui": "^0.9.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "web-vitals": "^5.0.3", "zod": "^4.0.0-beta.20250505T195954"}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.24.7", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.27.1", "@cloudflare/next-on-pages": "^1.13.7", "@playwright/test": "^1.53.1", "@tanstack/react-query": "^5.81.5", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/dompurify": "^3.0.5", "@types/jest": "^29.5.14", "@types/node": "^20.17.13", "@types/nodemailer": "^6.4.17", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@types/react-query": "^1.2.8", "autoprefixer": "^10.4.20", "babel-jest": "^30.0.4", "chalk": "^5.4.1", "commander": "^14.0.0", "csv-writer": "^1.6.0", "eslint": "^8.56.0", "eslint-config-next": "15.1.4", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.0", "jsdom": "^26.1.0", "postcss": "^8", "supabase": "^2.6.8", "tailwindcss": "^3.4.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}