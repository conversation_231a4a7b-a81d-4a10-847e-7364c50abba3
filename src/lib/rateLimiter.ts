/**
 * Server-side rate limiting utility
 * 
 * This module provides rate limiting functionality for API routes
 * to prevent abuse and excessive requests.
 */

import { NextRequest, NextResponse } from 'next/server';

// In-memory store for rate limiting
// Note: In a production environment with multiple instances,
// you would want to use Redis or another distributed cache
const ipRequestCounts = new Map<string, { count: number, resetTime: number }>();

interface RateLimitConfig {
  // Maximum number of requests allowed in the time window
  maxRequests: number;
  // Time window in seconds
  windowSizeInSeconds: number;
  // Optional identifier for the rate limit (useful for logging)
  identifier?: string;
}

// Default rate limit configurations
const defaultRateLimit: RateLimitConfig = {
  maxRequests: 60,
  windowSizeInSeconds: 60, // 60 requests per minute
  identifier: 'default'
};

const searchRateLimit: RateLimitConfig = {
  maxRequests: 100,
  windowSizeInSeconds: 60, // 100 requests per minute (aligned with requirements)
  identifier: 'search'
};

const productRateLimit: RateLimitConfig = {
  maxRequests: 30,
  windowSizeInSeconds: 60, // 30 requests per minute
  identifier: 'product'
};

const contactRateLimit: RateLimitConfig = {
  maxRequests: 5,
  windowSizeInSeconds: 600, // 5 requests per 10 minutes (stricter as per requirements)
  identifier: 'contact'
};

const brandsRateLimit: RateLimitConfig = {
  maxRequests: 40,
  windowSizeInSeconds: 60, // 40 requests per minute
  identifier: 'brands'
};

const retailersRateLimit: RateLimitConfig = {
  maxRequests: 35,
  windowSizeInSeconds: 60, // 35 requests per minute
  identifier: 'retailers'
};

/**
 * Applies rate limiting to an API route
 * 
 * @param request The Next.js request object
 * @param config Rate limit configuration
 * @returns NextResponse if rate limit is exceeded, null otherwise
 */
export function applyRateLimit(
  request: NextRequest,
  config: RateLimitConfig = defaultRateLimit
): NextResponse | null {
  // Get client IP from headers or connection
  const forwarded = request.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0].trim() : 'unknown';
  const now = Date.now();
  const rateLimitKey = `${ip}:${config.identifier || 'default'}`;
  
  // Get or initialize request count for this IP
  let requestData = ipRequestCounts.get(rateLimitKey);
  
  if (!requestData || now > requestData.resetTime) {
    // Initialize or reset if time window has passed
    requestData = { 
      count: 0, 
      resetTime: now + (config.windowSizeInSeconds * 1000) 
    };
  }
  
  // Increment request count
  requestData.count++;
  ipRequestCounts.set(rateLimitKey, requestData);
  
  // Check if rate limit is exceeded
  if (requestData.count > config.maxRequests) {
    // Calculate time until reset
    const timeUntilReset = Math.ceil((requestData.resetTime - now) / 1000);
    
    // Log rate limit exceeded
    console.warn(`Rate limit exceeded for ${ip} on ${config.identifier || 'API'}`);
    
    // Return rate limit response
    return new NextResponse(
      JSON.stringify({
        error: 'Too many requests',
        message: `Rate limit exceeded. Try again in ${timeUntilReset} seconds.`
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'X-RateLimit-Limit': config.maxRequests.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': Math.ceil(requestData.resetTime / 1000).toString(),
          'Retry-After': timeUntilReset.toString()
        }
      }
    );
  }
  
  // No rate limit exceeded, return null to continue processing
  return null;
}

// Export rate limit configurations
export const rateLimits = {
  default: defaultRateLimit,
  search: searchRateLimit,
  product: productRateLimit,
  contact: contactRateLimit,
  brands: brandsRateLimit,
  retailers: retailersRateLimit
};

/**
 * Cleans up old rate limit entries
 * Should be called periodically to prevent memory leaks
 */
export function cleanupRateLimits(): void {
  const now = Date.now();
  for (const [key, data] of ipRequestCounts.entries()) {
    if (now > data.resetTime) {
      ipRequestCounts.delete(key);
    }
  }
}

// Set up automatic cleanup every hour
if (typeof setInterval !== 'undefined') {
  setInterval(cleanupRateLimits, 60 * 60 * 1000);
}
