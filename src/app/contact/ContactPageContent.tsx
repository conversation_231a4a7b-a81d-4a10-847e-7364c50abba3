'use client'; // Mark as client component to handle interactivity

// Import necessary libraries and components
import { motion } from 'framer-motion'; // For animations
import { Send } from 'lucide-react'; // Icons for the form
import Link from 'next/link'; // Link component for navigation
import { useState } from 'react'; // For form state management
import { Turnstile } from '@marsidev/react-turnstile'; // Cloudflare Turnstile CAPTCHA

// Define ContactPageContent component - this is a client component
// that handles all the interactive UI elements while maintaining the design
export default function ContactPageContent() {
  // State for form feedback - controlled through React
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState('');
  // State for Turnstile CAPTCHA token
  const [turnstileToken, setTurnstileToken] = useState<string | null>(null);

  // Handle form submission with client-side logic
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault(); // Preventing default form submission behavior

    // Check if Turnstile token is present
    if (!turnstileToken) {
      setSubmitError('Please complete the CAPTCHA verification.');
      return;
    }

    setIsSubmitting(true);
    setSubmitSuccess(false);
    setSubmitError('');

    const formData = new FormData(e.currentTarget); // Collecting form data

    // Convert FormData to JSON and include Turnstile token
    const formDataObj: Record<string, string> = {};
    formData.forEach((value, key) => {
      formDataObj[key] = value.toString();
    });

    // Add Turnstile token to the form data
    formDataObj['cf-turnstile-response'] = turnstileToken;

    try {
      // Send form data to the API endpoint
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formDataObj),
      });
      
      // Error handling for failed submission
      if (!response.ok) {
        throw new Error('Failed to submit form');
      }
      
      // Handle successful submission
      setSubmitSuccess(true);
      console.log('Form submitted successfully');
      
      // Optional: redirect to thank you page
      // Delayed redirect to show success message
      setTimeout(() => {
        window.location.href = '/contact/thank-you';
      }, 1000);
      
    } catch (error) {
      console.error('Error submitting form:', error);
      // Set error state to display to user
      setSubmitError('There was a problem submitting your form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    // Main container for the contact page
    <div className="relative flex flex-col min-h-screen">
      {/* Hero section with animation */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary/10 via-secondary/10 to-background py-20"
      > 
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="max-w-3xl"
          >
            {/* Page heading - important for SEO hierarchy */}
            <h1 className="text-4xl font-bold text-primary mb-6">Contact Us</h1>
            <p className="text-lg text-foreground/80"> 
              We'd love to hear from you! Our team is always happy to help with any questions or feedback. 
            </p> 
          </motion.div>
        </div>
      </motion.section>

      {/* Form container section */}
      <div className="container py-16">
        <div className="max-w-3xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mb-10"
          >
            <p className="text-foreground/80 mb-6">
              Whether you need help with finding the right rebates, have a business inquiry, or just want to say hello,
              we're here for you. We aim to respond to all inquiries within 24 hours during business days.
            </p>
          </motion.div>

          {/* Form with animation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-xl shadow-sm border border-primary/10 p-8"
          >
            {/* Show success message if form submitted successfully */}
            {submitSuccess && (
              <div className="bg-green-50 border border-green-200 text-green-700 p-4 rounded-lg mb-6">
                Thank you for your message! We'll get back to you shortly.
              </div>
            )}
            
            {/* Show error message if form submission failed */}
            {submitError && (
              <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg mb-6">
                {submitError}
              </div>
            )}

            {/* Contact form with client-side handling */}
            <form 
              // Using both action (for non-JS fallback) and onSubmit for enhanced experience
              action="/api/contact" 
              method="POST"
              onSubmit={handleSubmit}
              className="space-y-6"
            >
              {/* Name input field */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-foreground mb-2" aria-label="Your Name">
                  Your Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  className="w-full px-4 py-2 rounded-lg border border-foreground/20 focus:outline-none focus:ring-2 focus:ring-primary/20"
                />
              </div> 

              {/* Email input field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-foreground mb-2" aria-label="Email Address">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  className="w-full px-4 py-2 rounded-lg border border-foreground/20 focus:outline-none focus:ring-2 focus:ring-primary/20"
                />
              </div> 

              {/* Phone input field (optional) */}
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-foreground mb-2" aria-label="Phone Number">
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  className="w-full px-4 py-2 rounded-lg border border-foreground/20 focus:outline-none focus:ring-2 focus:ring-primary/20"
                />
              </div> 

              {/* Inquiry type selection */}
              <div>
                <label htmlFor="enquiryType" className="block text-sm font-medium text-foreground mb-2" aria-label="Inquiry Type">
                  Inquiry Type *
                </label>
                <select
                  id="enquiryType"
                  name="enquiryType"
                  required
                  className="w-full px-4 py-2 rounded-lg border border-foreground/20 focus:outline-none focus:ring-2 focus:ring-primary/20"
                >
                  <option value="Customer Support" aria-label="Customer Support">Customer Support</option>
                  <option value="Business Enquiry" aria-label="Business Enquiry">Business Enquiry</option>
                  <option value="Other" aria-label="Other">Other</option>
                </select>
              </div> 

              {/* Message input field */}
              <div>
                <label htmlFor="message" className="block text-sm font-medium text-foreground mb-2" aria-label="Your Message">
                  Your Message *
                </label>
                <textarea
                  id="message"
                  name="message"
                  required
                  rows={5}
                  className="w-full px-4 py-2 rounded-lg border border-foreground/20 focus:outline-none focus:ring-2 focus:ring-primary/20"
                />
              </div> 

              {/* Cloudflare Turnstile CAPTCHA */}
              <div className="pt-2">
                <label className="block text-sm font-medium text-foreground mb-2">
                  Security Verification *
                </label>
                <div className="mb-4">
                  <Turnstile
                    siteKey={process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY || 'test-key'}
                    onSuccess={(token) => {
                      setTurnstileToken(token);
                      setSubmitError(''); // Clear any previous CAPTCHA errors
                    }}
                    onError={() => {
                      setTurnstileToken(null);
                      setSubmitError('CAPTCHA verification failed. Please try again.');
                    }}
                    onExpire={() => {
                      setTurnstileToken(null);
                      setSubmitError('CAPTCHA verification expired. Please verify again.');
                    }}
                    theme="light"
                    size="normal"
                  />
                </div>
              </div>

              {/* Terms and submit button section */}
              <div className="pt-2">
                <p className="text-sm text-foreground/60 mb-6" aria-label="Terms and Conditions">
                  By submitting this form, you agree to our{' '}
                  <Link href="/terms" className="text-primary hover:underline" aria-label="Terms & Conditions">
                    Terms & Conditions
                  </Link>
                  .
                </p>
                {/* Submit button with icon - with loading state */}
                <button
                  type="submit"
                  disabled={isSubmitting || !turnstileToken}
                  className="relative inline-flex items-center justify-center gap-2 bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-all w-full sm:w-auto disabled:opacity-70"
                >
                  <span className="flex items-center gap-2" aria-label="Send Message">
                    {isSubmitting ? 'Sending...' : 'Send Message'} <Send className="h-4 w-4" />
                  </span>
                </button>
              </div>
            </form> 
          </motion.div> 
        </div> 
      </div> 
    </div>
  );
}